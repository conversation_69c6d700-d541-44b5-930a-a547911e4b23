import constate from 'constate';
import {createContext, ReactNode, useCallback, useContext, useState} from 'react';
import {ChatAction} from '@/types/staff/element';

export type InPlatformHandler = (action: ChatAction) => void | Promise<void>;

interface InPlatformContextValue {
    registerHandler: (handler: InPlatformHandler) => void;
    unregisterHandler: (handler: InPlatformHandler) => void;
    executeHandler: (action: ChatAction) => Promise<void>;
}

const useInPlatformContextRaw = (): InPlatformContextValue => {
    const [handlers, setHandlers] = useState<Set<InPlatformHandler>>(new Set());

    const registerHandler = useCallback(
        (handler: InPlatformHandler) => {
            setHandlers(prev => new Set([...prev, handler]));
        },
        []
    );

    const unregisterHandler = useCallback(
        (handler: InPlatformHandler) => {
            setHandlers(prev => {
                const newSet = new Set(prev);
                newSet.delete(handler);
                return newSet;
            });
        },
        []
    );

    const executeHandler = useCallback(
        async (action: ChatAction) => {
            const handlerArray = Array.from(handlers);
            for (const handler of handlerArray) {
                try {
                    await handler(action);
                } catch (error) {
                    console.error('InPlatform handler execution failed:', error);
                }
            }
        },
        [handlers]
    );

    return {
        registerHandler,
        unregisterHandler,
        executeHandler,
    };
};

export const [
    InPlatformProviderConstate,
    useInPlatformContext,
] = constate(useInPlatformContextRaw);

const defaultInPlatformContext: InPlatformContextValue = {
    registerHandler: () => {},
    unregisterHandler: () => {},
    executeHandler: async () => {},
};

const InPlatformContextDirect = createContext<InPlatformContextValue>(defaultInPlatformContext);

export const InPlatformProvider = ({children}: {children: ReactNode}) => {
    const contextValue = useInPlatformContextRaw();
    return (
        <InPlatformContextDirect.Provider value={contextValue}>
            {children}
        </InPlatformContextDirect.Provider>
    );
};

export const useInPlatformContextSafe = (): InPlatformContextValue => {
    return useContext(InPlatformContextDirect);
};
