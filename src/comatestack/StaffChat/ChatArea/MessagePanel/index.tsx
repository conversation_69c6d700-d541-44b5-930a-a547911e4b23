import {Flex} from 'antd';
import {
    RefObject,
    useCallback,
    useEffect,
    useMemo,
    useRef,
} from 'react';
import styled from '@emotion/styled';
import {debounce} from 'lodash';
import {Messages} from '@/components/Chat/Message';
import {useCurrentChat} from '@/regions/staff/chat';
import {TimeDistanceText} from '@/components/Chat/Message/TimeDistanceText';
import {ConversationIdProvider} from '@/components/Chat/Provider/ConversationIdProvider';
import ChatMessageInput from '@/components/Chat/ChatMessageInput';
import {useLockChat} from '@/regions/staff/lockChat';
import {apiPostChatMessage} from '@/api/staff';
import {useCurrentAgentId} from '@/regions/staff/agent';
import {recognizeKuLinkFromText} from '@/hooks/common/kuLinkRecognize';
import {InPlatformProvider} from './InPlatformContext';

const Container = styled.div`
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 2;
    overflow: auto;
    width: 100%;
    ::-webkit-scrollbar {
        display: none;
    }
`;

const scrollBottom = (contentRef: RefObject<HTMLElement>) => {
    if (contentRef?.current) {
        const height = contentRef.current.scrollHeight;
        contentRef.current.scrollTo({top: height, behavior: 'smooth'});
    }
};

const debounceScrollBottom = debounce(scrollBottom, 500);

export const MessagePanel = () => {
    const contentRef = useRef(null);
    const lockChat = useLockChat();
    const mainConatinerId = 'main_container_001';
    const {messageIds, createTime, conversationId, meta, running} = useCurrentChat();
    const currentAgentId = useCurrentAgentId();

    useEffect(
        () => {
            if (messageIds?.length && contentRef?.current) {
                debounceScrollBottom(contentRef);
            }
        },
        [messageIds?.length]
    );

    // 只有文思匠可以停止生成
    const cancelDisabled = currentAgentId !== 2;
    const [disabled, disabledReason] = useMemo(
        () => {
            let disabled = false;
            let disabledReason = '';
            if (lockChat) {
                disabled = true;
                disabledReason = '正在工作中，请不要打扰我哦';
            }
            if (!conversationId) {
                disabled = true;
                disabledReason = '当前会话不存在，请重新发起会话';
            }
            if (meta?.disabled) {
                disabled = true;
                disabledReason = meta?.disabledReason || '当前任务已完成';
            }
            if (currentAgentId === 4) {
                disabled = true;
                disabledReason = '智测师暂未开放对话功能';
            }
            if (currentAgentId === 9) {
                disabled = true;
                disabledReason = '我还不能在这里接受任务委托，去如流上搜索『前端君』就能找到我啦';
            }
            return [disabled, disabledReason];
        },
        [conversationId, currentAgentId, lockChat, meta?.disabled, meta?.disabledReason]
    );

    const sendMessage = useCallback(
        async (chatValue: string) => {
            const kuContent = currentAgentId === 2 ? await recognizeKuLinkFromText(chatValue) : [];
            return apiPostChatMessage({
                agentId: currentAgentId,
                conversationId,
                fromCallback: false,
                kuContent,
                elements: [
                    {
                        type: 'smartText',
                        content: chatValue,
                    },
                ],
            });
        },
        [conversationId, currentAgentId]
    );

    return (
        <InPlatformProvider>
            <ConversationIdProvider conversationId={conversationId}>
                <Container id={mainConatinerId} ref={contentRef}>
                    <Flex align="center" justify="center">
                        <TimeDistanceText time={createTime} />
                    </Flex>
                    <Messages />
                </Container>
                <ChatMessageInput
                    isPending={running}
                    disabled={disabled}
                    cancelDisabled={cancelDisabled || meta?.cancelDisabled}
                    disabledReason={disabledReason}
                    onSend={sendMessage}
                />
            </ConversationIdProvider>
        </InPlatformProvider>
    );
};
